// Google-style simple layout
.app-container {
  height: 100vh;
  width: 100vw;
  background: #de3341; // Background.bg-brand - Light.brand.500 (Rocket) color
  font-family: 'Google Sans', 'Roboto', sans-serif;
  position: relative;
  overflow: hidden;
}

// Left Sidebar - Full viewport height, behind main content
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 64px; // Collapsed width (icon only)
  height: 100vh;
  background: #de3341; // Background.bg-brand - Light.brand.500 (Rocket) color
  z-index: 1; // Behind main content
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, width 0.25s ease-out;
  overflow: hidden;

  &.visible {
    opacity: 1;
    visibility: visible;
  }

  &.expanded {
    width: 280px; // Expanded width (icon + text)
  }
}

.sidebar-content {
  padding: 24px 0; // No top padding needed since header is in content
  transform: translateX(-20px);
  transition: transform 0.3s ease 0.1s; // Slight delay for stagger effect

  .sidebar.visible & {
    transform: translateX(0);
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px; // Adjusted padding for collapsed state
  cursor: pointer;
  transition: background-color 0.2s ease, opacity 0.3s ease, transform 0.3s ease, padding 0.3s ease;
  color: rgba(255, 255, 255, 0.9); // White text for blue background
  opacity: 0;
  transform: translateX(-10px);
  justify-content: flex-start;
  white-space: nowrap;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1); // Light white overlay on hover
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.9); // White icons
    flex-shrink: 0; // Prevent icon from shrinking
  }

  .nav-text {
    font-size: 14px;
    font-weight: 400;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px);
    transition: all 0.25s ease;
    white-space: nowrap;
    overflow: hidden;
  }

  // When sidebar is expanded, show text
  .sidebar.expanded & .nav-text {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    transition-delay: 0.1s; // Delay text appearance until sidebar has expanded
  }

  // Adjust padding when expanded
  .sidebar.expanded & {
    padding: 12px 24px;
  }

  // Staggered animation for each nav item
  .sidebar.visible & {
    opacity: 1;
    transform: translateX(0);

    &:nth-child(1) { transition-delay: 0.1s; }
    &:nth-child(2) { transition-delay: 0.15s; }
    &:nth-child(3) { transition-delay: 0.2s; }
    &:nth-child(4) { transition-delay: 0.25s; }
  }
}

// Main Content - Slides right when menu opens, appears above sidebar
.main-content {
  height: 100vh;
  width: 100vw;
  background: #ffffff;
  transition: transform 0.25s ease-out,
              border-radius 0.25s ease-out,
              box-shadow 0.25s ease,
              width 0.25s ease-out;
  position: relative;
  z-index: 2; // Above sidebar
  border-radius: 0 0 0 0; // Rounded top-left corner
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); // Subtle shadow
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.shifted {
    transform: translateX(64px); // Shift by collapsed sidebar width
    width: calc(100vw - 64px); // Reduce width to fit within viewport
    border-radius: 35px 0 0 35px; // Rounded left corners when shifted
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.15), 0 0 20px rgba(0, 0, 0, 0.1);
  }

  &.shifted.expanded {
    transform: translateX(280px); // Shift by expanded sidebar width on hover
    width: calc(100vw - 280px); // Reduce width to fit within viewport when expanded
  }
}

// Header Component inside content
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  flex-shrink: 0; // Don't shrink
}

.menu-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(60, 64, 67, 0.08);
  }

  mat-icon {
    color: #5f6368;
    font-size: 24px;
  }
}

.loan-display {
  font-size: 16px;
  color: #202124;
  font-weight: 400;
}

.user-menu {
  position: relative;

  .profile-btn {
    background: #ffffff;
    border: 1px solid #e8eaed;
    padding: 8px 12px;
    height: 48px;
    min-height: 48px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    // Always maintain rounded rectangle shape, but adjust width
    border-radius: 24px;
    width: 48px; // Start as circle width
    transition: width 0.3s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      border-color: #dadce0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }
  }

  // Expanded state - pull apart to reveal content
  &.expanded .profile-btn {
    width: auto; // Allow natural width expansion
    min-width: 180px; // Minimum width when expanded
    padding: 8px 16px 8px 12px; // Adjust padding for expanded state
  }

  // Circular state styling (but shape stays rounded rectangle)
  &.circular .profile-btn {
    width: 48px; // Collapsed width
    padding: 8px; // Centered padding
  }

  &.circular .profile-content {
    justify-content: center;
    min-width: 32px;
  }

  .profile-content {
    display: flex;
    align-items: center;
    gap: 0; // Start with no gap
    min-width: 32px; // Minimum width when collapsed
    transition: gap 0.3s ease;
    position: relative;
    white-space: nowrap;
  }

  // Expanded state - add gap between elements
  &.expanded .profile-content {
    gap: 12px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #de3341; // Rocket brand color
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    flex-shrink: 0;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px); // Start further left
    transition: all 0.3s ease 0.1s; // Slight delay for stagger effect
    white-space: nowrap;
    overflow: hidden;
    width: 0; // Start with zero width
    margin-left: 0; // No margin when collapsed
  }

  // Expanded state - slide in details
  &.expanded .user-details {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    width: auto; // Allow natural width
    margin-left: 0; // Maintain spacing
  }

  // Hide details and icon in circular state
  &.circular .user-details,
  &.circular .dropdown-icon {
    display: none;
  }

  .user-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
    line-height: 1.2;
  }

  .user-role {
    font-size: 12px;
    color: #5f6368;
    line-height: 1.2;
  }

  .dropdown-icon {
    font-size: 18px;
    color: #5f6368;
    opacity: 0;
    visibility: hidden;
    transform: translateX(10px); // Start from the right
    transition: all 0.3s ease 0.15s; // Longer delay for last element
    flex-shrink: 0;
    width: 0; // Start with zero width
    margin-left: 0;
  }

  // Expanded state - slide in dropdown icon
  &.expanded .dropdown-icon {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    width: 18px; // Natural icon width
    margin-left: 0;
  }

  // Expanded state hover effect
  &.expanded .profile-btn {
    background-color: #f8f9fa;
    border-color: #dadce0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

// Profile dropdown menu styling
.profile-dropdown {
  .mat-mdc-menu-panel {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8eaed;
    min-width: 220px;
    margin-top: 8px;
  }

  .mat-mdc-menu-item {
    height: 48px;
    padding: 0 16px;
    font-size: 14px;

    &:hover {
      background-color: #f8f9fa;
    }

    mat-icon {
      margin-right: 12px;
      color: #5f6368;
      font-size: 20px;
    }

    &.logout-item {
      color: #d93025;

      mat-icon {
        color: #d93025;
      }

      &:hover {
        background-color: #fce8e6;
      }
    }
  }

  .mat-mdc-menu-divider {
    margin: 8px 0;
    border-color: #e8eaed;
  }
}

// Page Content Area
.page-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

// Responsive Design
@media (max-width: 768px) {
  .sidebar {
    width: 56px; // Smaller collapsed width on mobile

    &.expanded {
      width: 240px; // Smaller expanded width on mobile
    }
  }

  .main-content.shifted {
    transform: translateX(56px); // Shift by mobile collapsed width
    width: calc(100vw - 56px); // Reduce width to fit within viewport on mobile

    &.expanded {
      transform: translateX(240px); // Shift by mobile expanded sidebar width on hover
      width: calc(100vw - 240px); // Reduce width to fit within viewport when expanded on mobile
    }
  }

  .header {
    padding: 12px 16px;
  }

  .loan-display {
    font-size: 14px;
  }

  // Profile menu adjustments for mobile
  .user-menu {
    &.expanded .user-details {
      max-width: 120px; // Smaller expansion on mobile
    }
  }
}
