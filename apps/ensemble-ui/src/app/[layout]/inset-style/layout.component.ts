import { Component, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { LayoutService } from '../layout.service';

@Component({
  selector: 'ens-inset-layout',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule
  ],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss'
})
export class InsetLayoutComponent implements OnInit {
  private readonly layoutService = inject(LayoutService);
  private hoverTimeout: any = null; // For delayed hover trigger

  appTitle = 'Rocket Logic Ensemble';
  activeLoanNumber = '123456789'; // This would come from your loan service
  sideMenuExpanded = false; // Track hover state for menu expansion

  // User profile data
  user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Loan Officer',
    avatar: 'JS'
  };

  // Layout state from service
  get sideMenuOpen() {
    return this.layoutService.sideMenuOpen;
  }

  get bottomSheetOpen() {
    return this.layoutService.bottomSheetOpen;
  }

  ngOnInit() {
    // Open the sidebar by default to show the collapsed icon menu
    this.openSideMenu();
  }

  // User actions
  logout() {
    // Implement logout functionality
    console.log('Logging out...');
  }

  openProfile() {
    // Implement profile functionality
    console.log('Opening profile...');
  }

  openSettings() {
    // Implement settings functionality
    console.log('Opening settings...');
  }

  // Layout control methods - delegate to service
  openSideMenu() {
    this.layoutService.openSideMenu();
  }

  closeSideMenu() {
    this.layoutService.closeSideMenu();
  }

  openBottomSheet() {
    this.layoutService.openBottomSheet();
  }

  closeBottomSheet() {
    this.layoutService.closeBottomSheet();
  }

  // Delayed hover methods for side menu
  onSideMenuMouseEnter() {
    // Clear any existing timeout
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }

    // Set a delay before expanding the menu
    this.hoverTimeout = setTimeout(() => {
      this.sideMenuExpanded = true;
    }, 300); // 300ms delay
  }

  onSideMenuMouseLeave() {
    // Clear any pending expansion
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }

    // Immediately collapse the menu
    this.sideMenuExpanded = false;
  }

  // Navigation methods
  navigateToLoanSearch() {
    console.log('Navigating to Loan Search...');
  }

  navigateToLoanStatus() {
    console.log('Navigating to Loan Status...');
  }

  navigateToLoanTrace() {
    console.log('Navigating to Loan Trace...');
  }

  navigateToLogs() {
    console.log('Navigating to Logs...');
  }
}
