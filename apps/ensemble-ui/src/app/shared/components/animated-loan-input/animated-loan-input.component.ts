import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'ens-animated-loan-input',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './animated-loan-input.component.html',
  styleUrls: ['./animated-loan-input.component.scss']
})
export class AnimatedLoanInputComponent implements OnInit, OnDestroy {
  @Input() placeholder = 'Enter loan number';
  @Input() maxLength = 10; // Standard loan number length
  @Input() showPlaceholdersAfter = 1; // Show placeholders after this many digits
  @Output() valueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<string>();

  private destroy$ = new Subject<void>();
  
  inputControl = new FormControl('');
  displayDigits: string[] = [];
  animatingDigits: Set<number> = new Set();

  ngOnInit(): void {
    this.inputControl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.updateDisplayDigits(value || '');
        this.valueChange.emit(value || '');
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateDisplayDigits(value: string): void {
    const newDigits = value.split('');
    const oldLength = this.displayDigits.length;
    const newLength = newDigits.length;

    // Handle adding digits
    if (newLength > oldLength) {
      for (let i = oldLength; i < newLength; i++) {
        this.animatingDigits.add(i);
        setTimeout(() => {
          this.animatingDigits.delete(i);
        }, 300);
      }
    }

    // Handle removing digits
    if (newLength < oldLength) {
      for (let i = newLength; i < oldLength; i++) {
        this.animatingDigits.add(i);
        setTimeout(() => {
          this.animatingDigits.delete(i);
        }, 300);
      }
    }

    this.displayDigits = newDigits;
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.enterPressed.emit(this.inputControl.value || '');
    }
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value.replace(/\D/g, ''); // Only allow digits
    
    if (value.length <= this.maxLength) {
      this.inputControl.setValue(value, { emitEvent: false });
      this.updateDisplayDigits(value);
      this.valueChange.emit(value);
    } else {
      // Prevent exceeding max length
      const truncated = value.substring(0, this.maxLength);
      this.inputControl.setValue(truncated, { emitEvent: false });
      target.value = truncated;
    }
  }

  isDigitAnimating(index: number): boolean {
    return this.animatingDigits.has(index);
  }

  getDigitAtIndex(index: number): string {
    return this.displayDigits[index] || '';
  }

  focus(): void {
    const input = document.querySelector('.animated-input') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  }

  trackByIndex(index: number): number {
    return index;
  }

  shouldShowPlaceholders(): boolean {
    return this.displayDigits.length >= this.showPlaceholdersAfter &&
           this.displayDigits.length < this.maxLength;
  }

  getPlaceholderDots(): any[] {
    if (!this.shouldShowPlaceholders()) {
      return [];
    }

    const remainingDigits = this.maxLength - this.displayDigits.length;
    return new Array(remainingDigits).fill(null);
  }
}
